import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/face_guide_overlay.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';

/// Test file to verify face verification fixes
/// 
/// This test verifies:
/// 1. Face guide overlay painting logic is correct
/// 2. Widget disposal doesn't cause Navigator errors
/// 3. Resource cleanup works properly
void main() {
  group('Face Verification Fixes', () {
    testWidgets('FaceGuideOverlay paints correctly', (WidgetTester tester) async {
      // Test that the face guide overlay renders without errors
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 400,
              height: 600,
              child: FaceGuideOverlay(
                currentDetection: FaceDetectionResult(
                  faceBounds: const Rect.fromLTWH(100, 150, 200, 250),
                  confidence: 0.9,
                  timestamp: DateTime.now(),
                  imageSize: const Size(400, 600),
                ),
                isRecording: false,
              ),
            ),
          ),
        ),
      );

      // Verify the widget builds without errors
      expect(find.byType(FaceGuideOverlay), findsOneWidget);
      
      // Pump a few frames to ensure animations work
      await tester.pump(const Duration(milliseconds: 100));
      await tester.pump(const Duration(milliseconds: 100));
      
      // No exceptions should be thrown
    });

    testWidgets('FaceGuideOverlay handles recording state', (WidgetTester tester) async {
      // Test recording state changes
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 400,
              height: 600,
              child: FaceGuideOverlay(
                currentDetection: FaceDetectionResult(
                  faceBounds: const Rect.fromLTWH(100, 150, 200, 250),
                  confidence: 0.85,
                  timestamp: DateTime.now(),
                  imageSize: const Size(400, 600),
                ),
                isRecording: true,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(FaceGuideOverlay), findsOneWidget);
      
      // Test state change
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 400,
              height: 600,
              child: FaceGuideOverlay(
                currentDetection: FaceDetectionResult(
                  faceBounds: const Rect.fromLTWH(100, 150, 200, 250),
                  confidence: 0.85,
                  timestamp: DateTime.now(),
                  imageSize: const Size(400, 600),
                ),
                isRecording: false,
              ),
            ),
          ),
        ),
      );
      
      await tester.pump();
    });

    testWidgets('FaceGuideOverlay disposes properly', (WidgetTester tester) async {
      // Test that disposal doesn't cause errors
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 400,
              height: 600,
              child: FaceGuideOverlay(
                currentDetection: null,
                isRecording: false,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(FaceGuideOverlay), findsOneWidget);
      
      // Remove the widget to test disposal
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 400,
              height: 600,
              child: Text('Disposed'),
            ),
          ),
        ),
      );
      
      await tester.pump();
      
      // Should not find the overlay anymore
      expect(find.byType(FaceGuideOverlay), findsNothing);
    });
  });
}
